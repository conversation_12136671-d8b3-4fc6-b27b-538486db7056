{"name": "ccjap-backend", "version": "1.0.0", "description": "Backend API for CCJAP Sistema de Gestion Docente", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:init": "node scripts/init-db.js", "db:migrate": "node scripts/run-migrations.js", "db:reset": "node scripts/init-db.js && node scripts/run-migrations.js", "db:n8n": "node scripts/create-n8n-db.js", "dev:full": "npm run db:reset && npm run db:n8n && nodemon server.js"}, "keywords": ["api", "express", "nodejs"], "author": "Cline", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "path": "^0.12.7", "pg": "^8.11.5", "pg-format": "^1.0.4", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.1.0"}, "type": "commonjs"}