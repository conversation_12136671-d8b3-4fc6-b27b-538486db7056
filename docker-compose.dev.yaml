version: '3.8'

services:
  # Base de datos PostgreSQL para desarrollo local
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: ccjapuser
      POSTGRES_PASSWORD: ccjappassword
      POSTGRES_DB: ccjapdb
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ccjapuser"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
